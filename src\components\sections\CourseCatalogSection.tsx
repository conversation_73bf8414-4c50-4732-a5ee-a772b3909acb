"use client";

import { useCallback, useEffect, useMemo, useState } from "react";
import { useCourseCatalog } from "@/services/course";
import { ICourseCatalogItem } from "@px-shared-account/hermes";
import { ProductCard, ProductCardSkeleton } from "@/components/cards/ProductCard";
import { PageSectionBase, PageSectionCarousel } from "../base/page-section";
import { useTranslations } from "next-intl";

export function CourseCatalogSection() {
  const t = useTranslations("dashboard");
  const [cursor, setCursor] = useState<number | undefined>(0);
  const { data, isLoading } = useCourseCatalog({ limit: 20, cursor });
  const courses: ICourseCatalogItem[] = data?.data || [];

  const loadMoreCourses = useCallback(() => {
    if (data?.nextCursor !== null && data?.nextCursor !== undefined) {
      setCursor(data.nextCursor);
    }
  }, [cursor, data]);

  const { ownedCourses, purchasableCourses } = useMemo(() => {
    const ownedCourses: ICourseCatalogItem[] = [];
    const purchasableCourses: ICourseCatalogItem[] = [];
    for (const course of courses) {
      if (course.owned) {
        ownedCourses.push(course);
      } else {
        purchasableCourses.push(course);
      }
    }
    return { ownedCourses, purchasableCourses };
  }, [courses]);

  if (isLoading && courses.length === 0) {
    return (
      <PageSectionCarousel title={t("courses.title")} description={t("courses.sectionSubtitle")}>
        {Array.from({ length: 12 }).map((_, index) => (
          <ProductCardSkeleton key={index} />
        ))}
      </PageSectionCarousel>
    );
  }

  return (
    <div className="flex flex-col gap-8">
      {ownedCourses.length === 1 ? (
        <PageSectionBase title={t("purchases.title")} description={t("purchases.sectionSubtitle")}>
          <div className="flex justify-center py-4">
            <ProductCard course={ownedCourses[0]} />
          </div>
        </PageSectionBase>
      ) : ownedCourses.length > 1 ? (
        <PageSectionCarousel
          title={t("purchases.title")}
          description={t("purchases.sectionSubtitle")}
          carouselItemsClassName="basis-full sm:basis-1/2 md:basis-auto"
          carouselOpts={{ loop: ownedCourses.length > 3 }}
        >
          {ownedCourses.map((course) => (
            <ProductCard key={course.id} course={course} />
          ))}
        </PageSectionCarousel>
      ) : null}

      {isLoading ? (
        <PageSectionCarousel title={t("courses.title")} description={t("courses.sectionSubtitle")}>
          {Array.from({ length: 12 }).map((_, index) => (
            <ProductCardSkeleton key={index} />
          ))}
        </PageSectionCarousel>
      ) : purchasableCourses.length === 0 ? (
        <PageSectionBase title={t("courses.title")} description={t("courses.sectionSubtitle")}>
          <div className="p-8 text-center">
            <p>{t("courses.empty-state")}</p>
          </div>
        </PageSectionBase>
      ) : purchasableCourses.length === 1 ? (
        <PageSectionBase title={t("courses.title")} description={t("courses.sectionSubtitle")}>
          <div className="flex justify-center py-4">
            <ProductCard course={purchasableCourses[0]} />
          </div>
        </PageSectionBase>
      ) : (
        <PageSectionCarousel
          title={t("courses.title")}
          description={t("courses.sectionSubtitle")}
          carouselItemsClassName="basis-full sm:basis-1/2 md:basis-auto"
          carouselOpts={{ loop: false }}
          onEnd={() => {
            console.log("onEnd", data?.nextCursor, cursor);
            if (data?.nextCursor !== null && data?.nextCursor !== undefined) {
              loadMoreCourses();
            }
          }}
        >
          {purchasableCourses.map((course) => (
            <ProductCard key={course.id} course={course} />
          ))}
        </PageSectionCarousel>
      )}
    </div>
  );
}
