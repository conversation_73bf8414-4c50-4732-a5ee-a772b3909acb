"use client";

import { cn } from "@/lib/utils";
import React, { useState, useEffect, Children } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
  type CarouselApi,
} from "@/components/ui/carousel";
import type useEmblaCarousel from "embla-carousel-react";
import { DotIndicator } from "./DotIndicator";

// CarouselWrapper Props
export interface CarouselWrapperProps {
  children: React.ReactNode;
  opts?: Parameters<typeof useEmblaCarousel>[0];
  itemClassName?: string;
  contentClassName?: string;
  showArrows?: boolean;
  showDots?: boolean;
  arrowLeftClassName?: string;
  arrowRightClassName?: string;
  dotsContainerClassName?: string;
  wrapperClassName?: string;
  onEnd?: () => void;
}

export const CarouselWrapper = ({
  children,
  opts,
  itemClassName,
  contentClassName,
  showArrows = true,
  showDots = true,
  arrowLeftClassName,
  arrowRightClassName,
  dotsContainerClassName,
  wrapperClassName = "relative w-full",
  onEnd,
}: CarouselWrapperProps) => {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const numChildren = Children.count(children);

  useEffect(() => {
    if (!api) return;
    const updateState = () => {
      if (api.scrollSnapList().length === 0 && numChildren > 0) api.reInit();
      setCount(api.scrollSnapList().length);
      setCurrent(api.selectedScrollSnap() + 1);
    };
    updateState();
    api.on("select", updateState);
    api.on("slidesChanged", updateState);
    api.on("reInit", updateState);
    const timer = setTimeout(updateState, 100);
    return () => {
      clearTimeout(timer);
      api.off("select", updateState);
      api.off("slidesChanged", updateState);
      api.off("reInit", updateState);
    };
  }, [api, numChildren]);

  useEffect(() => {
    if (api && current !== 0 && current === count) {
      onEnd?.();
    }
  }, [api, current, count, onEnd]);

  if (numChildren === 0) return null;

  return (
    <Carousel
      opts={{
        align: "start",
        loop: opts?.loop !== undefined ? opts.loop : numChildren > 1,
        ...opts,
      }}
      setApi={setApi}
      className={cn(wrapperClassName)}
    >
      <div className="flex flex-col gap-4">
        <CarouselContent className={cn("-ml-4 flex gap-4 p-2", contentClassName)}>
          {React.Children.map(children, (child, index) => (
            <CarouselItem key={index} className={cn("pl-2 select-none", itemClassName)}>
              {child}
            </CarouselItem>
          ))}
        </CarouselContent>
        <div className="flex items-center justify-between">
          {showArrows && numChildren > 1 && count > 1 && (
            <CarouselPrevious
              className={cn(
                "bg-primary hover:bg-primary/80 static translate-x-0 translate-y-0 disabled:grayscale",
                arrowLeftClassName,
              )}
            />
          )}
          {showDots && (
            <DotIndicator
              count={count}
              current={current}
              onDotClick={(index) => api?.scrollTo(index)}
              className={dotsContainerClassName}
            />
          )}
          {showArrows && numChildren > 1 && count > 1 && (
            <CarouselNext
              className={cn(
                "bg-primary hover:bg-primary/80 static translate-x-0 translate-y-0 disabled:grayscale",
                arrowRightClassName,
              )}
            />
          )}
        </div>
      </div>
    </Carousel>
  );
};

CarouselWrapper.displayName = "CarouselWrapper";
